/// Application Localizations
///
/// Provides localization support for the Towasl application
/// Replaces GetX translations with Flutter's standard localization system
library app_localizations;

import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_ar.dart';
import 'app_localizations_en.dart';

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
abstract class AppLocalizations {
  AppLocalizations(String locale)
      : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('ar'),
    Locale('en'),
  ];

  // Common strings
  String get cancel;
  String get save;
  String get next;
  String get go;
  String get sec;

  // Authentication
  String get enterMobileNumber;
  String get mobileNumber;
  String get toReSendOtpCode;
  String get incorrectOtpError;
  String get avoidBlockage;
  String get otpResentSuccessfully;
  String get verificationCode;
  String get pleaseEnterCompleteOtp;
  String get pleaseEnterMobileNumber;
  String get pleaseEnterValidMobileNumber;
  String get loginFirst;

  // Interests
  String get selectYourInterests;

  // Location and Privacy
  String get fetchingLocation;
  String get privacyFirstDesc;
  String get needAllowLocation;
  String get allowLocation;

  // Home
  String get noRecordFound;

  // Validation
  String get required;
  String get invalidYear;
  String get incorrect;
  String get mobileFormat;
  String get invalidEmailFormat;
  String get nameTooShort;
  String get passwordTooShort;

  // Welcome
  String get welcomeToTowasl;
  String get welcomeDescription;
  String get getStarted;

  // Location
  String get homeDistrict;
  String get selectDistrict;
  String get districtSelection;
  String get pleaseSelectDistrict;
  String get noDistrictsAvailable;
  String get searchDistricts;
  String get noDistrictsFound;
  String get pleaseEnableGpsAndAllowLocation;
  String get unknownCity;
  String get unknownCountry;
  String get gpsLocationRequired;
  String get gettingLocation;
  String get getLocation;
  String get locationPermissionRequired;
  String get continueText;

  // Authentication
  String get welcomeToTowasI;
  String get enterMobileNumberToGetStarted;
  String get iAcceptThe;
  String get termsAndConditions;
  String get and;
  String get privacyPolicy;
  String get couldNotLaunch;

  // Interests
  String get yourInterests;
  String get selectAtLeastThreeInterests;
  String get loadingInterests;
  String get noInterestsAvailable;

  // Personal Info
  String get personalInformation;
  String get tellUsAboutYourself;
  String get personalInfoDescription;
  String get birthYear;
  String get enterYourBirthYear;
  String get pleaseEnterYourBirthYear;
  String get pleaseEnterValidBirthYear;
  String get gender;
  String get selectYourGender;
  String get nationality;
  String get selectYourNationality;

  // Home
  String get towasl;
  String get loading;
  String get regionNotSupported;
  String get regionNotSupportedDescription;

  // Profile
  String get profile;
  String get myProfile;
  String get interests;
  String get personalInfo;
  String get location;
  String get signOut;
  String get signOutConfirmation;
  String get noInterestsSelected;
  String interestsCount(int count);
  String get notSet;
  String get yearsOld;
  String get loggingOut;

  // App Update
  String get updateRequired;
  String get updateAvailable;
  String get mandatoryUpdateDescription;
  String get optionalUpdateDescription;
  String get currentVersion;
  String get newVersion;
  String get updateApp;
  String get skipNow;
  String get cannotOpenAppStore;
  String get updateLinkNotAvailable;
  String get errorOpeningAppStore;

  // OTP Verification
  String get verifyMobileNumber;
  String get enterVerificationCode;
  String get weSentCodeTo;
  String get resendCodeIn;
  String get didntReceiveCode;
  String get resend;
  String get verify;

  // Toast Messages
  String get otpSentSuccessfully;
  String get loginFailed;
  String get anErrorOccurred;
  String get otpVerifiedSuccessfully;
  String get otpVerificationFailed;
  String get pleaseWaitBeforeRequestingOtp;
  String get failedToResendOtp;
  String get numberBlockedCantLogin;
  String get profileSavedSuccessfully;
  String get failedToSaveProfile;
  String get anErrorOccurredWhileSavingPersonalInfo;
  String get accountDeletedSuccessfully;
  String get failedToDeleteAccount;
  String get anErrorOccurredWhileSavingLocation;
  String get openingAppSettings;
  String get interestsUpdatedSuccessfully;
  String get locationUpdatedSuccessfully;

  // Gender Options (for display)
  String get male;
  String get female;

  // Nationality Options (for display - common ones)
  String get saudi;
  String get egyptian;
  String get jordanian;
  String get lebanese;
  String get syrian;
  String get iraqi;
  String get kuwaiti;
  String get emirati;
  String get qatari;
  String get bahraini;
  String get omani;
  String get yemeni;
  String get palestinian;
  String get moroccan;
  String get tunisian;
  String get algerian;
  String get libyan;
  String get sudanese;
  String get somali;
  String get mauritanian;
  String get djiboutian;
  String get other;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['ar', 'en'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'ar':
      return AppLocalizationsAr();
    case 'en':
      return AppLocalizationsEn();
  }

  throw FlutterError(
      'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
      'an issue with the localizations generation tool. Please file an issue on GitHub with a '
      'reproducible sample app and the gen-l10n configuration that was used.');
}
